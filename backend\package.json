{"name": "backend", "version": "1.0.0", "main": "server.ts", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "start": "node dist/server.js", "build": "tsc"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^1.41.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "multer": "^2.0.0", "multer-storage-cloudinary": "^4.0.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.23", "nodemon": "^3.1.10", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}
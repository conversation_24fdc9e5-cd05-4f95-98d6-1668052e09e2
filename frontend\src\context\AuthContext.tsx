import { createContext } from 'react';
import axios from 'axios';
interface User {
  name: string;
  email: string;
  password: string;
}

export type AuthContextType = {
  register: (user: User) => void;
  login: (user: { email: string; password: string }) => void;
  logout: () => void;
};

export type AuthContextProviderProps = {
  children: React.ReactNode;
};

export const AuthContext = createContext<AuthContextType>({
  register: () => {},
  login: () => {},
  logout: () => {},
});

export const AuthContextProvider = ({ children }: AuthContextProviderProps) => {
  const BACKEND_URL = import.meta.env.VITE_BACKEND_URL;

  const register = async (user: User) => {
    await axios.post(`${BACKEND_URL}/api/auth/register`, user, {
      withCredentials: true,
    });
    console.log('Registered');
  };

  const login = async (user: { email: string; password: string }) => {
    await axios.post(`${BACKEND_URL}/api/auth/login`, user, {
      withCredentials: true,
    });
    console.log('Logged in');
  };

  const logout = async () => {
    await axios.post(
      `${BACKEND_URL}/api/auth/logout`,
      {},
      {
        withCredentials: true,
      }
    );
    console.log('Logged out');
  };

  return (
    <AuthContext.Provider value={{ register, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};
